using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    public enum InvoiceType
    {
        Sales = 1,          // فاتورة مبيعات
        Purchase = 2,       // فاتورة مشتريات
        SalesReturn = 3,    // مردود مبيعات
        PurchaseReturn = 4  // مردود مشتريات
    }

    public enum InvoiceStatus
    {
        Draft = 1,      // مسودة
        Confirmed = 2,  // مؤكدة
        Paid = 3,       // مدفوعة
        Cancelled = 4   // ملغية
    }

    [Table("Invoices")]
    public class Invoice
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;

        public InvoiceType InvoiceType { get; set; }

        public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;

        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        public DateTime? DueDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } = 0;

        [StringLength(500)]
        public string? Notes { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Foreign Keys
        public int? CustomerId { get; set; }
        public int? SupplierId { get; set; }
        public int UserId { get; set; }

        // Navigation properties
        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }

        [ForeignKey("SupplierId")]
        public virtual Supplier? Supplier { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        public virtual ICollection<InvoiceDetail> InvoiceDetails { get; set; } = new List<InvoiceDetail>();
        public virtual ICollection<Receipt> Receipts { get; set; } = new List<Receipt>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();

        // Computed properties
        [NotMapped]
        public decimal RemainingAmount => TotalAmount - PaidAmount;

        [NotMapped]
        public bool IsFullyPaid => PaidAmount >= TotalAmount;

        [NotMapped]
        public string CustomerSupplierName
        {
            get
            {
                return InvoiceType == InvoiceType.Sales || InvoiceType == InvoiceType.SalesReturn
                    ? Customer?.Name ?? ""
                    : Supplier?.Name ?? "";
            }
        }

        [NotMapped]
        public string TypeDescription
        {
            get
            {
                return InvoiceType switch
                {
                    InvoiceType.Sales => "فاتورة مبيعات",
                    InvoiceType.Purchase => "فاتورة مشتريات",
                    InvoiceType.SalesReturn => "مردود مبيعات",
                    InvoiceType.PurchaseReturn => "مردود مشتريات",
                    _ => ""
                };
            }
        }

        [NotMapped]
        public string StatusDescription
        {
            get
            {
                return Status switch
                {
                    InvoiceStatus.Draft => "مسودة",
                    InvoiceStatus.Confirmed => "مؤكدة",
                    InvoiceStatus.Paid => "مدفوعة",
                    InvoiceStatus.Cancelled => "ملغية",
                    _ => ""
                };
            }
        }
    }
}
