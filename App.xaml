<Application x:Class="AccountingSystem.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- RTL Support -->
                    <Style TargetType="{x:Type Window}">
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, <PERSON>l"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>
                    
                    <Style TargetType="{x:Type UserControl}">
                        <Setter Property="FlowDirection" Value="RightToLeft"/>
                        <Setter Property="FontFamily" Value="Segoe UI, Tahoma, Arial"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>
                    
                    <!-- Custom Button Style -->
                    <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                    
                    <!-- Custom TextBox Style -->
                    <Style x:Key="PrimaryTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                    </Style>
                    
                    <!-- Custom ComboBox Style -->
                    <Style x:Key="PrimaryComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>
                    
                    <!-- Card Style -->
                    <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="10"/>
                        <Setter Property="Padding" Value="20"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
