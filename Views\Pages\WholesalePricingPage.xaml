<Page x:Class="AccountingSystem.Views.Pages.WholesalePricingPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="إدارة أسعار الجملة">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="TagMultiple" 
                                           Width="32" Height="32" 
                                           VerticalAlignment="Center" 
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="إدارة أسعار الجملة" 
                             FontSize="24" FontWeight="Bold" 
                             VerticalAlignment="Center" 
                             Margin="10,0,0,0"/>
                </StackPanel>

                <Button Grid.Column="1" 
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Content="إضافة سعر جملة"
                        Click="AddWholesalePrice_Click"
                        Margin="0,0,10,0">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16"/>
                            <TextBlock Text="إضافة سعر" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button Grid.Column="2" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Content="تحديث أسعار المنتجات"
                        Click="BulkUpdatePrices_Click">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Update" Width="16" Height="16"/>
                            <TextBlock Text="تحديث جماعي" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Search and Filter -->
        <materialDesign:Card Grid.Row="1" Padding="15" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Grid.Column="0" 
                         x:Name="SearchTextBox"
                         materialDesign:HintAssist.Hint="البحث في المنتجات..."
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         TextChanged="SearchTextBox_TextChanged"
                         Margin="0,0,10,0"/>

                <ComboBox Grid.Column="1" 
                          x:Name="CategoryComboBox"
                          materialDesign:HintAssist.Hint="الفئة"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          Width="150"
                          SelectionChanged="CategoryComboBox_SelectionChanged"
                          Margin="0,0,10,0"/>

                <CheckBox Grid.Column="2" 
                          x:Name="ActiveOnlyCheckBox"
                          Content="المنتجات النشطة فقط"
                          IsChecked="True"
                          Checked="ActiveOnlyCheckBox_Changed"
                          Unchecked="ActiveOnlyCheckBox_Changed"
                          Margin="0,0,10,0"/>

                <Button Grid.Column="3" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Content="تحديث"
                        Click="RefreshButton_Click">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16"/>
                            <TextBlock Text="تحديث" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Data Grid -->
        <materialDesign:Card Grid.Row="2" Padding="0">
            <DataGrid x:Name="WholesalePricingDataGrid"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      SelectionMode="Single"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      MouseDoubleClick="WholesalePricingDataGrid_MouseDoubleClick">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="كود المنتج" Binding="{Binding Product.Code}" Width="100"/>
                    <DataGridTextColumn Header="اسم المنتج" Binding="{Binding Product.Name}" Width="200"/>
                    <DataGridTextColumn Header="سعر التجزئة" Binding="{Binding Product.SalePrice, StringFormat=N2}" Width="100"/>
                    <DataGridTextColumn Header="سعر الجملة" Binding="{Binding Product.WholesalePrice, StringFormat=N2}" Width="100"/>
                    <DataGridTextColumn Header="الحد الأدنى للجملة" Binding="{Binding Product.MinimumWholesaleQuantity, StringFormat=N0}" Width="120"/>
                    <DataGridTextColumn Header="اسم الشريحة" Binding="{Binding TierName}" Width="120"/>
                    <DataGridTextColumn Header="نطاق الكمية" Binding="{Binding QuantityRange}" Width="120"/>
                    <DataGridTextColumn Header="السعر الفعال" Binding="{Binding EffectivePrice, StringFormat=N2}" Width="100"/>
                    <DataGridTextColumn Header="الخصم" Binding="{Binding DiscountDescription}" Width="120"/>
                    <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="60"/>
                    
                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            ToolTip="تعديل"
                                            Click="EditPricing_Click"
                                            Tag="{Binding Id}">
                                        <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            ToolTip="نسخ"
                                            Click="CopyPricing_Click"
                                            Tag="{Binding Id}">
                                        <materialDesign:PackIcon Kind="ContentCopy" Width="16" Height="16"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                            ToolTip="حذف"
                                            Click="DeletePricing_Click"
                                            Tag="{Binding Id}">
                                        <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</Page>
