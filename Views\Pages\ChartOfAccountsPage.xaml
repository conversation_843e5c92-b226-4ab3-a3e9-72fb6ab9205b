<Page x:Class="AccountingSystem.Views.Pages.ChartOfAccountsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="شجرة الحسابات">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                  Text="شجرة الحسابات" 
                  FontSize="24" 
                  FontWeight="Bold" 
                  Margin="0,0,0,20"/>

        <!-- Content -->
        <materialDesign:Card Grid.Row="1" Padding="20">
            <TextBlock Text="صفحة شجرة الحسابات - قيد التطوير" 
                     FontSize="16" 
                     HorizontalAlignment="Center" 
                     VerticalAlignment="Center"/>
        </materialDesign:Card>
    </Grid>
</Page>
