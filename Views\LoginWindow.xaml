<Window x:Class="AccountingSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام المحاسبة المالية" 
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="20,20,20,10" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <StackPanel Padding="20" Background="{DynamicResource PrimaryHueMidBrush}">
                <materialDesign:PackIcon Kind="AccountCircle" 
                                       Width="48" Height="48" 
                                       Foreground="White"
                                       HorizontalAlignment="Center"/>
                <TextBlock Text="نظام المحاسبة المالية" 
                         FontSize="20" FontWeight="Bold" 
                         Foreground="White"
                         HorizontalAlignment="Center" 
                         Margin="0,10,0,0"/>
                <TextBlock Text="تسجيل الدخول" 
                         FontSize="14" 
                         Foreground="White"
                         HorizontalAlignment="Center" 
                         Margin="0,5,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Login Form -->
        <materialDesign:Card Grid.Row="1" Margin="20,10,20,10" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <StackPanel Padding="30" VerticalAlignment="Center">
                
                <!-- Username -->
                <TextBox x:Name="UsernameTextBox"
                        materialDesign:HintAssist.Hint="اسم المستخدم"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        Margin="0,10"
                        FontSize="14"
                        Height="50"/>

                <!-- Password -->
                <PasswordBox x:Name="PasswordBox"
                           materialDesign:HintAssist.Hint="كلمة المرور"
                           Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                           Margin="0,10"
                           FontSize="14"
                           Height="50"/>

                <!-- Remember Me -->
                <CheckBox x:Name="RememberMeCheckBox"
                         Content="تذكرني"
                         Margin="0,15,0,10"
                         Style="{StaticResource MaterialDesignCheckBox}"/>

                <!-- Login Button -->
                <Button x:Name="LoginButton"
                       Content="تسجيل الدخول"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Background="{DynamicResource PrimaryHueMidBrush}"
                       Foreground="White"
                       Height="45"
                       FontSize="14"
                       FontWeight="Medium"
                       Margin="0,20,0,10"
                       Click="LoginButton_Click"/>

                <!-- Error Message -->
                <TextBlock x:Name="ErrorMessageTextBlock"
                         Foreground="Red"
                         FontSize="12"
                         TextWrapping="Wrap"
                         HorizontalAlignment="Center"
                         Margin="0,10,0,0"
                         Visibility="Collapsed"/>

                <!-- Loading Indicator -->
                <ProgressBar x:Name="LoadingProgressBar"
                           Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Width="30" Height="30"
                           IsIndeterminate="True"
                           Visibility="Collapsed"
                           Margin="0,10,0,0"/>

            </StackPanel>
        </materialDesign:Card>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                   HorizontalAlignment="Center" 
                   Margin="20,10,20,20">
            <TextBlock Text="© 2024 نظام المحاسبة المالية" 
                     FontSize="12" 
                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
        </StackPanel>

    </Grid>
</Window>
