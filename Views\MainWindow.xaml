<Window x:Class="AccountingSystem.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="نظام المحاسبة المالية" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Top Bar -->
            <materialDesign:ColorZone Grid.Row="0" 
                                    Mode="PrimaryMid" 
                                    Padding="16"
                                    materialDesign:ShadowAssist.ShadowDepth="Depth2">
                <DockPanel>
                    <!-- Menu Button -->
                    <Button x:Name="MenuButton"
                           DockPanel.Dock="Right"
                           Style="{StaticResource MaterialDesignIconButton}"
                           Foreground="White"
                           Click="MenuButton_Click">
                        <materialDesign:PackIcon Kind="Menu" Width="24" Height="24"/>
                    </Button>

                    <!-- User Info -->
                    <StackPanel DockPanel.Dock="Left" 
                               Orientation="Horizontal" 
                               VerticalAlignment="Center">
                        <materialDesign:PackIcon Kind="AccountCircle" 
                                               Width="32" Height="32" 
                                               Foreground="White"
                                               VerticalAlignment="Center"/>
                        <StackPanel Margin="10,0,0,0" VerticalAlignment="Center">
                            <TextBlock x:Name="UserNameTextBlock"
                                     Text="مرحباً، مدير النظام"
                                     Foreground="White"
                                     FontWeight="Medium"
                                     FontSize="14"/>
                            <TextBlock x:Name="UserRoleTextBlock"
                                     Text="مدير"
                                     Foreground="White"
                                     FontSize="12"
                                     Opacity="0.8"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Title -->
                    <TextBlock Text="نظام المحاسبة المالية"
                             HorizontalAlignment="Center"
                             VerticalAlignment="Center"
                             FontSize="20"
                             FontWeight="Medium"
                             Foreground="White"/>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Side Navigation -->
                <materialDesign:Card Grid.Column="0" 
                                   Margin="10,10,5,10"
                                   materialDesign:ShadowAssist.ShadowDepth="Depth2">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            
                            <!-- Dashboard -->
                            <Button x:Name="DashboardButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,15"
                                   Click="NavigationButton_Click"
                                   Tag="Dashboard">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ViewDashboard" 
                                                           Width="20" Height="20" 
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="لوحة التحكم" 
                                             Margin="10,0,0,0" 
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="10,5"/>

                            <!-- Accounts -->
                            <TextBlock Text="الحسابات" 
                                     Margin="20,10,20,5" 
                                     FontWeight="Medium" 
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            
                            <Button x:Name="ChartOfAccountsButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="ChartOfAccounts">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountTree" 
                                                           Width="18" Height="18" 
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="شجرة الحسابات" 
                                             Margin="10,0,0,0" 
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="JournalEntriesButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="JournalEntries">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="BookOpen" 
                                                           Width="18" Height="18" 
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="القيود اليومية" 
                                             Margin="10,0,0,0" 
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="10,5"/>

                            <!-- Customers & Suppliers -->
                            <TextBlock Text="العملاء والموردين" 
                                     Margin="20,10,20,5" 
                                     FontWeight="Medium" 
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            
                            <Button x:Name="CustomersButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="Customers">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountGroup" 
                                                           Width="18" Height="18" 
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="العملاء" 
                                             Margin="10,0,0,0" 
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="SuppliersButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="Suppliers">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="TruckDelivery" 
                                                           Width="18" Height="18" 
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="الموردين" 
                                             Margin="10,0,0,0" 
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="10,5"/>

                            <!-- Inventory -->
                            <TextBlock Text="المخزون" 
                                     Margin="20,10,20,5" 
                                     FontWeight="Medium" 
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            
                            <Button x:Name="ProductsButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="Products">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Package" 
                                                           Width="18" Height="18" 
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="الأصناف" 
                                             Margin="10,0,0,0" 
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="CategoriesButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="Categories">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Tag" 
                                                           Width="18" Height="18" 
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="فئات الأصناف" 
                                             Margin="10,0,0,0" 
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="10,5"/>

                            <!-- Wholesale Sales -->
                            <TextBlock Text="البيع بالجملة"
                                     Margin="20,10,20,5"
                                     FontWeight="Medium"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                            <Button x:Name="WholesaleCustomersButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="WholesaleCustomers">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountGroup"
                                                           Width="18" Height="18"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="عملاء الجملة"
                                             Margin="10,0,0,0"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="WholesaleInvoicesButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="WholesaleInvoices">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Receipt"
                                                           Width="18" Height="18"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="فواتير الجملة"
                                             Margin="10,0,0,0"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="WholesalePricingButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="WholesalePricing">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="TagMultiple"
                                                           Width="18" Height="18"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="أسعار الجملة"
                                             Margin="10,0,0,0"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="WholesaleReportsButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="WholesaleReports">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartLine"
                                                           Width="18" Height="18"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="تقارير الجملة"
                                             Margin="10,0,0,0"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="10,5"/>

                            <!-- Invoices -->
                            <TextBlock Text="الفواتير"
                                     Margin="20,10,20,5"
                                     FontWeight="Medium"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                            <Button x:Name="SalesInvoicesButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="SalesInvoices">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Receipt"
                                                           Width="18" Height="18"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="فواتير المبيعات"
                                             Margin="10,0,0,0"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="PurchaseInvoicesButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="PurchaseInvoices">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ShoppingCart"
                                                           Width="18" Height="18"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="فواتير المشتريات"
                                             Margin="10,0,0,0"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="10,5"/>

                            <!-- Reports -->
                            <TextBlock Text="التقارير" 
                                     Margin="20,10,20,5" 
                                     FontWeight="Medium" 
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            
                            <Button x:Name="ReportsButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,10"
                                   Click="NavigationButton_Click"
                                   Tag="Reports">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartLine" 
                                                           Width="18" Height="18" 
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="التقارير المالية" 
                                             Margin="10,0,0,0" 
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="10,5"/>

                            <!-- Settings -->
                            <Button x:Name="SettingsButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,15"
                                   Click="NavigationButton_Click"
                                   Tag="Settings">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Settings" 
                                                           Width="20" Height="20" 
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="الإعدادات" 
                                             Margin="10,0,0,0" 
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Logout -->
                            <Button x:Name="LogoutButton"
                                   Style="{StaticResource MaterialDesignFlatButton}"
                                   HorizontalAlignment="Stretch"
                                   HorizontalContentAlignment="Right"
                                   Padding="20,15"
                                   Click="LogoutButton_Click"
                                   Foreground="Red">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Logout" 
                                                           Width="20" Height="20" 
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="تسجيل الخروج" 
                                             Margin="10,0,0,0" 
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:Card>

                <!-- Content Area -->
                <materialDesign:Card Grid.Column="1" 
                                   Margin="5,10,10,10"
                                   materialDesign:ShadowAssist.ShadowDepth="Depth2">
                    <Frame x:Name="MainFrame" 
                          NavigationUIVisibility="Hidden"/>
                </materialDesign:Card>

            </Grid>

            <!-- Status Bar -->
            <materialDesign:ColorZone Grid.Row="2" 
                                    Mode="PrimaryLight" 
                                    Padding="16,8"
                                    materialDesign:ShadowAssist.ShadowDepth="Depth1">
                <DockPanel>
                    <TextBlock x:Name="StatusTextBlock"
                             Text="جاهز"
                             VerticalAlignment="Center"/>
                    
                    <TextBlock x:Name="DateTimeTextBlock"
                             DockPanel.Dock="Left"
                             Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='yyyy/MM/dd HH:mm'}"
                             VerticalAlignment="Center"/>
                </DockPanel>
            </materialDesign:ColorZone>

        </Grid>
    </materialDesign:DialogHost>
</Window>
