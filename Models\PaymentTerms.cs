using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    [Table("PaymentTerms")]
    public class PaymentTerms
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public int DueDays { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal EarlyPaymentDiscountPercentage { get; set; } = 0;

        public int EarlyPaymentDiscountDays { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal LateFeePercentage { get; set; } = 0;

        public int GracePeriodDays { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();

        // Computed properties
        [NotMapped]
        public string TermsDescription
        {
            get
            {
                var description = $"استحقاق خلال {DueDays} يوم";
                
                if (EarlyPaymentDiscountPercentage > 0)
                    description += $" - خصم {EarlyPaymentDiscountPercentage:N1}% للدفع خلال {EarlyPaymentDiscountDays} يوم";
                
                if (LateFeePercentage > 0)
                    description += $" - غرامة تأخير {LateFeePercentage:N1}% بعد {GracePeriodDays} يوم";
                
                return description;
            }
        }

        [NotMapped]
        public bool HasEarlyPaymentDiscount => EarlyPaymentDiscountPercentage > 0 && EarlyPaymentDiscountDays > 0;

        [NotMapped]
        public bool HasLateFee => LateFeePercentage > 0;
    }
}
