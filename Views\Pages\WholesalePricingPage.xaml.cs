using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.EntityFrameworkCore;
using AccountingSystem.Data;
using AccountingSystem.Models;

namespace AccountingSystem.Views.Pages
{
    public partial class WholesalePricingPage : Page
    {
        public WholesalePricingPage()
        {
            InitializeComponent();
            LoadCategories();
            LoadWholesalePricing();
        }

        private async void LoadCategories()
        {
            try
            {
                using var context = new AccountingDbContext();
                var categories = await context.ProductCategories
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                var categoryList = new List<object>
                {
                    new { Id = (int?)null, Name = "جميع الفئات" }
                };
                categoryList.AddRange(categories.Select(c => new { Id = (int?)c.Id, Name = c.Name }));

                CategoryComboBox.ItemsSource = categoryList;
                CategoryComboBox.DisplayMemberPath = "Name";
                CategoryComboBox.SelectedValuePath = "Id";
                CategoryComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadWholesalePricing()
        {
            try
            {
                using var context = new AccountingDbContext();
                
                var query = context.WholesalePricings
                    .Include(wp => wp.Product)
                    .ThenInclude(p => p.Category)
                    .AsQueryable();

                // Apply filters
                if (ActiveOnlyCheckBox.IsChecked == true)
                {
                    query = query.Where(wp => wp.IsActive && wp.Product.IsActive);
                }

                if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    var searchTerm = SearchTextBox.Text.Trim().ToLower();
                    query = query.Where(wp => wp.Product.Name.ToLower().Contains(searchTerm) ||
                                            wp.Product.Code.ToLower().Contains(searchTerm) ||
                                            wp.TierName.ToLower().Contains(searchTerm));
                }

                if (CategoryComboBox.SelectedValue is int selectedCategoryId)
                {
                    query = query.Where(wp => wp.Product.CategoryId == selectedCategoryId);
                }

                var pricingData = await query.OrderBy(wp => wp.Product.Code).ToListAsync();
                WholesalePricingDataGrid.ItemsSource = pricingData;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات أسعار الجملة: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddWholesalePrice_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Open Add Wholesale Pricing Dialog
            MessageBox.Show("سيتم فتح نافذة إضافة سعر جملة جديد", "قيد التطوير", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BulkUpdatePrices_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Open Bulk Update Prices Dialog
            MessageBox.Show("سيتم فتح نافذة التحديث الجماعي للأسعار", "قيد التطوير", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditPricing_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int pricingId)
            {
                // TODO: Open Edit Pricing Dialog
                MessageBox.Show($"سيتم تعديل السعر رقم: {pricingId}", "قيد التطوير", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void CopyPricing_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int pricingId)
            {
                // TODO: Copy pricing to other products
                MessageBox.Show($"سيتم نسخ السعر رقم: {pricingId}", "قيد التطوير", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeletePricing_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int pricingId)
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف هذا السعر؟", "تأكيد الحذف", 
                                           MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    // TODO: Implement delete functionality
                    MessageBox.Show($"سيتم حذف السعر رقم: {pricingId}", "قيد التطوير", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            LoadWholesalePricing();
        }

        private void CategoryComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            LoadWholesalePricing();
        }

        private void ActiveOnlyCheckBox_Changed(object sender, RoutedEventArgs e)
        {
            LoadWholesalePricing();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadWholesalePricing();
        }

        private void WholesalePricingDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (WholesalePricingDataGrid.SelectedItem is WholesalePricing pricing)
            {
                EditPricing_Click(new Button { Tag = pricing.Id }, new RoutedEventArgs());
            }
        }
    }
}
