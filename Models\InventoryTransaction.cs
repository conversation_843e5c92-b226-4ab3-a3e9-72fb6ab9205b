using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    public enum TransactionType
    {
        Purchase = 1,       // شراء
        Sale = 2,          // بيع
        Adjustment = 3,    // تسوية
        Transfer = 4,      // تحويل
        Return = 5         // مردود
    }

    [Table("InventoryTransactions")]
    public class InventoryTransaction
    {
        [Key]
        public int Id { get; set; }

        public TransactionType TransactionType { get; set; }

        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,4)")]
        public decimal Quantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; } = 0;

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(100)]
        public string? Reference { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Foreign Keys
        public int ProductId { get; set; }
        public int? InvoiceId { get; set; }
        public int UserId { get; set; }

        // Navigation properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        [ForeignKey("InvoiceId")]
        public virtual Invoice? Invoice { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        // Computed properties
        [NotMapped]
        public string TransactionTypeDescription
        {
            get
            {
                return TransactionType switch
                {
                    TransactionType.Purchase => "شراء",
                    TransactionType.Sale => "بيع",
                    TransactionType.Adjustment => "تسوية",
                    TransactionType.Transfer => "تحويل",
                    TransactionType.Return => "مردود",
                    _ => ""
                };
            }
        }
    }
}
