using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    [Table("JournalEntryDetails")]
    public class JournalEntryDetail
    {
        [Key]
        public int Id { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditAmount { get; set; } = 0;

        [StringLength(500)]
        public string? Description { get; set; }

        // Foreign Keys
        public int JournalEntryId { get; set; }
        public int AccountId { get; set; }

        // Navigation properties
        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry JournalEntry { get; set; } = null!;

        [ForeignKey("AccountId")]
        public virtual Account Account { get; set; } = null!;

        // Computed properties
        [NotMapped]
        public decimal Amount => DebitAmount > 0 ? DebitAmount : CreditAmount;

        [NotMapped]
        public string TransactionType => DebitAmount > 0 ? "مدين" : "دائن";
    }
}
