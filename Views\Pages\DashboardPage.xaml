<Page x:Class="AccountingSystem.Views.Pages.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="لوحة التحكم">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <TextBlock Grid.Row="0" 
                      Text="لوحة التحكم" 
                      FontSize="24" 
                      FontWeight="Bold" 
                      Margin="0,0,0,20"/>

            <!-- Summary Cards -->
            <UniformGrid Grid.Row="1" Columns="4" Margin="0,0,0,20">
                
                <!-- Total Sales -->
                <materialDesign:Card Margin="5" 
                                   Padding="20"
                                   Background="{DynamicResource PrimaryHueMidBrush}">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="TrendingUp" 
                                                   Width="32" Height="32" 
                                                   Foreground="White"
                                                   DockPanel.Dock="Right"/>
                            <StackPanel DockPanel.Dock="Left">
                                <TextBlock Text="إجمالي المبيعات" 
                                         Foreground="White" 
                                         FontSize="14"/>
                                <TextBlock x:Name="TotalSalesTextBlock"
                                         Text="0.00 ر.س" 
                                         Foreground="White" 
                                         FontSize="20" 
                                         FontWeight="Bold"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Purchases -->
                <materialDesign:Card Margin="5" 
                                   Padding="20"
                                   Background="{DynamicResource SecondaryHueMidBrush}">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="TrendingDown" 
                                                   Width="32" Height="32" 
                                                   Foreground="White"
                                                   DockPanel.Dock="Right"/>
                            <StackPanel DockPanel.Dock="Left">
                                <TextBlock Text="إجمالي المشتريات" 
                                         Foreground="White" 
                                         FontSize="14"/>
                                <TextBlock x:Name="TotalPurchasesTextBlock"
                                         Text="0.00 ر.س" 
                                         Foreground="White" 
                                         FontSize="20" 
                                         FontWeight="Bold"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Customers -->
                <materialDesign:Card Margin="5" 
                                   Padding="20"
                                   Background="#FF4CAF50">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="AccountGroup" 
                                                   Width="32" Height="32" 
                                                   Foreground="White"
                                                   DockPanel.Dock="Right"/>
                            <StackPanel DockPanel.Dock="Left">
                                <TextBlock Text="عدد العملاء" 
                                         Foreground="White" 
                                         FontSize="14"/>
                                <TextBlock x:Name="TotalCustomersTextBlock"
                                         Text="0" 
                                         Foreground="White" 
                                         FontSize="20" 
                                         FontWeight="Bold"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Products -->
                <materialDesign:Card Margin="5" 
                                   Padding="20"
                                   Background="#FFFF9800">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="Package" 
                                                   Width="32" Height="32" 
                                                   Foreground="White"
                                                   DockPanel.Dock="Right"/>
                            <StackPanel DockPanel.Dock="Left">
                                <TextBlock Text="عدد الأصناف" 
                                         Foreground="White" 
                                         FontSize="14"/>
                                <TextBlock x:Name="TotalProductsTextBlock"
                                         Text="0" 
                                         Foreground="White" 
                                         FontSize="20" 
                                         FontWeight="Bold"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>

            </UniformGrid>

            <!-- Quick Actions -->
            <materialDesign:Card Grid.Row="2" 
                               Margin="0,0,0,20" 
                               Padding="20">
                <StackPanel>
                    <TextBlock Text="الإجراءات السريعة" 
                             FontSize="18" 
                             FontWeight="Medium" 
                             Margin="0,0,0,15"/>
                    
                    <UniformGrid Columns="4">
                        <Button x:Name="NewSalesInvoiceButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Margin="5"
                               Padding="15"
                               Click="QuickActionButton_Click"
                               Tag="NewSalesInvoice">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Receipt" 
                                                       Width="24" Height="24" 
                                                       HorizontalAlignment="Center"/>
                                <TextBlock Text="فاتورة مبيعات جديدة" 
                                         HorizontalAlignment="Center" 
                                         Margin="0,5,0,0"
                                         TextWrapping="Wrap"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="NewPurchaseInvoiceButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Margin="5"
                               Padding="15"
                               Click="QuickActionButton_Click"
                               Tag="NewPurchaseInvoice">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="ShoppingCart" 
                                                       Width="24" Height="24" 
                                                       HorizontalAlignment="Center"/>
                                <TextBlock Text="فاتورة مشتريات جديدة" 
                                         HorizontalAlignment="Center" 
                                         Margin="0,5,0,0"
                                         TextWrapping="Wrap"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="NewCustomerButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Margin="5"
                               Padding="15"
                               Click="QuickActionButton_Click"
                               Tag="NewCustomer">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="AccountPlus" 
                                                       Width="24" Height="24" 
                                                       HorizontalAlignment="Center"/>
                                <TextBlock Text="عميل جديد" 
                                         HorizontalAlignment="Center" 
                                         Margin="0,5,0,0"
                                         TextWrapping="Wrap"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="NewProductButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Margin="5"
                               Padding="15"
                               Click="QuickActionButton_Click"
                               Tag="NewProduct">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="PackagePlus" 
                                                       Width="24" Height="24" 
                                                       HorizontalAlignment="Center"/>
                                <TextBlock Text="صنف جديد" 
                                         HorizontalAlignment="Center" 
                                         Margin="0,5,0,0"
                                         TextWrapping="Wrap"/>
                            </StackPanel>
                        </Button>
                    </UniformGrid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Recent Activities -->
            <Grid Grid.Row="3">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Recent Invoices -->
                <materialDesign:Card Grid.Column="0" 
                                   Margin="0,0,10,0" 
                                   Padding="20">
                    <StackPanel>
                        <TextBlock Text="آخر الفواتير" 
                                 FontSize="18" 
                                 FontWeight="Medium" 
                                 Margin="0,0,0,15"/>
                        
                        <DataGrid x:Name="RecentInvoicesDataGrid"
                                AutoGenerateColumns="False"
                                CanUserAddRows="False"
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                HeadersVisibility="Column"
                                GridLinesVisibility="Horizontal"
                                Style="{StaticResource MaterialDesignDataGrid}">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="رقم الفاتورة" 
                                                  Binding="{Binding InvoiceNumber}" 
                                                  Width="100"/>
                                <DataGridTextColumn Header="النوع" 
                                                  Binding="{Binding TypeDescription}" 
                                                  Width="100"/>
                                <DataGridTextColumn Header="المبلغ" 
                                                  Binding="{Binding TotalAmount, StringFormat='{}{0:N2}'}" 
                                                  Width="100"/>
                                <DataGridTextColumn Header="التاريخ" 
                                                  Binding="{Binding InvoiceDate, StringFormat='{}{0:yyyy/MM/dd}'}" 
                                                  Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Low Stock Products -->
                <materialDesign:Card Grid.Column="1" 
                                   Margin="10,0,0,0" 
                                   Padding="20">
                    <StackPanel>
                        <TextBlock Text="أصناف منخفضة المخزون" 
                                 FontSize="18" 
                                 FontWeight="Medium" 
                                 Margin="0,0,0,15"/>
                        
                        <DataGrid x:Name="LowStockProductsDataGrid"
                                AutoGenerateColumns="False"
                                CanUserAddRows="False"
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                HeadersVisibility="Column"
                                GridLinesVisibility="Horizontal"
                                Style="{StaticResource MaterialDesignDataGrid}">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="كود الصنف" 
                                                  Binding="{Binding Code}" 
                                                  Width="80"/>
                                <DataGridTextColumn Header="اسم الصنف" 
                                                  Binding="{Binding Name}" 
                                                  Width="150"/>
                                <DataGridTextColumn Header="المخزون الحالي" 
                                                  Binding="{Binding CurrentStock, StringFormat='{}{0:N2}'}" 
                                                  Width="100"/>
                                <DataGridTextColumn Header="الحد الأدنى" 
                                                  Binding="{Binding MinimumStock, StringFormat='{}{0:N2}'}" 
                                                  Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>

            </Grid>

        </Grid>
    </ScrollViewer>
</Page>
