using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    public enum AccountType
    {
        Asset = 1,      // أصول
        Liability = 2,  // خصوم
        Equity = 3,     // حقوق الملكية
        Revenue = 4,    // إيرادات
        Expense = 5     // مصاريف
    }

    [Table("Accounts")]
    public class Account
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        public string Code { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        public AccountType AccountType { get; set; }

        public int? ParentId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        public string? Description { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("ParentId")]
        public virtual Account? Parent { get; set; }

        public virtual ICollection<Account> Children { get; set; } = new List<Account>();
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();

        // Computed properties
        [NotMapped]
        public string FullName => $"{Code} - {Name}";

        [NotMapped]
        public bool HasChildren => Children.Any();

        [NotMapped]
        public int Level
        {
            get
            {
                int level = 0;
                var parent = Parent;
                while (parent != null)
                {
                    level++;
                    parent = parent.Parent;
                }
                return level;
            }
        }
    }
}
