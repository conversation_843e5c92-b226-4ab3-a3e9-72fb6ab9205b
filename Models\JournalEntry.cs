using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    [Table("JournalEntries")]
    public class JournalEntry
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string EntryNumber { get; set; } = string.Empty;

        public DateTime EntryDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDebit { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCredit { get; set; } = 0;

        public bool IsPosted { get; set; } = false;

        public DateTime? PostedDate { get; set; }

        [StringLength(100)]
        public string? Reference { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Foreign Keys
        public int UserId { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();

        // Computed properties
        [NotMapped]
        public bool IsBalanced => TotalDebit == TotalCredit;

        [NotMapped]
        public string StatusDescription => IsPosted ? "مرحل" : "غير مرحل";
    }
}
