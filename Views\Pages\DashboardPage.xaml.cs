using System.Windows;
using System.Windows.Controls;
using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace AccountingSystem.Views.Pages
{
    public partial class DashboardPage : Page
    {
        public DashboardPage()
        {
            InitializeComponent();
            LoadDashboardData();
        }

        private async void LoadDashboardData()
        {
            try
            {
                using var context = new AccountingDbContext();

                // Load summary data
                await LoadSummaryData(context);
                
                // Load recent invoices
                await LoadRecentInvoices(context);
                
                // Load low stock products
                await LoadLowStockProducts(context);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadSummaryData(AccountingDbContext context)
        {
            // Total Sales (current month)
            var currentMonth = DateTime.Now.Month;
            var currentYear = DateTime.Now.Year;
            
            var totalSales = await context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Sales && 
                           i.InvoiceDate.Month == currentMonth && 
                           i.InvoiceDate.Year == currentYear)
                .SumAsync(i => i.TotalAmount);
            
            TotalSalesTextBlock.Text = $"{totalSales:N2} ر.س";

            // Total Purchases (current month)
            var totalPurchases = await context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Purchase && 
                           i.InvoiceDate.Month == currentMonth && 
                           i.InvoiceDate.Year == currentYear)
                .SumAsync(i => i.TotalAmount);
            
            TotalPurchasesTextBlock.Text = $"{totalPurchases:N2} ر.س";

            // Total Customers
            var totalCustomers = await context.Customers
                .Where(c => c.IsActive)
                .CountAsync();
            
            TotalCustomersTextBlock.Text = totalCustomers.ToString();

            // Total Products
            var totalProducts = await context.Products
                .Where(p => p.IsActive)
                .CountAsync();
            
            TotalProductsTextBlock.Text = totalProducts.ToString();
        }

        private async Task LoadRecentInvoices(AccountingDbContext context)
        {
            var recentInvoices = await context.Invoices
                .Include(i => i.Customer)
                .Include(i => i.Supplier)
                .OrderByDescending(i => i.InvoiceDate)
                .Take(10)
                .ToListAsync();

            RecentInvoicesDataGrid.ItemsSource = recentInvoices;
        }

        private async Task LoadLowStockProducts(AccountingDbContext context)
        {
            var lowStockProducts = await context.Products
                .Where(p => p.IsActive && p.TrackInventory && p.CurrentStock <= p.MinimumStock)
                .OrderBy(p => p.CurrentStock)
                .Take(10)
                .ToListAsync();

            LowStockProductsDataGrid.ItemsSource = lowStockProducts;
        }

        private void QuickActionButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string action)
            {
                try
                {
                    switch (action)
                    {
                        case "NewSalesInvoice":
                            // Navigate to new sales invoice
                            NavigateToPage("SalesInvoices");
                            break;
                        case "NewPurchaseInvoice":
                            // Navigate to new purchase invoice
                            NavigateToPage("PurchaseInvoices");
                            break;
                        case "NewCustomer":
                            // Navigate to customers page
                            NavigateToPage("Customers");
                            break;
                        case "NewProduct":
                            // Navigate to products page
                            NavigateToPage("Products");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تنفيذ الإجراء: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void NavigateToPage(string pageTag)
        {
            // Find the main window and navigate
            var mainWindow = Application.Current.Windows.OfType<MainWindow>().FirstOrDefault();
            if (mainWindow != null)
            {
                // This would require exposing a navigation method in MainWindow
                // For now, we'll show a message
                MessageBox.Show($"سيتم الانتقال إلى {GetPageTitle(pageTag)}", "معلومات", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private string GetPageTitle(string pageTag)
        {
            return pageTag switch
            {
                "SalesInvoices" => "فواتير المبيعات",
                "PurchaseInvoices" => "فواتير المشتريات",
                "Customers" => "العملاء",
                "Products" => "الأصناف",
                _ => ""
            };
        }
    }
}
