using System.Configuration;
using System.Data;
using System.Windows;
using AccountingSystem.Data;
using Microsoft.EntityFrameworkCore;
using System.Globalization;
using System.Threading;

namespace AccountingSystem
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            // Set Arabic culture for RTL support
            var culture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
            CultureInfo.DefaultThreadCurrentCulture = culture;
            CultureInfo.DefaultThreadCurrentUICulture = culture;

            // Initialize database
            try
            {
                using var context = new AccountingDbContext();
                context.Database.EnsureCreated();
                
                // Seed initial data if needed
                SeedInitialData(context);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الاتصال بقاعدة البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
                return;
            }

            base.OnStartup(e);
        }

        private void SeedInitialData(AccountingDbContext context)
        {
            // Check if admin user exists
            if (!context.Users.Any(u => u.Username == "admin"))
            {
                // Create default admin user
                var adminUser = new Models.User
                {
                    Username = "admin",
                    Password = HashPassword("admin123"), // In production, use proper hashing
                    FullName = "مدير النظام",
                    Role = Models.UserRole.Admin,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };
                
                context.Users.Add(adminUser);
                context.SaveChanges();
            }

            // Seed default chart of accounts if empty
            if (!context.Accounts.Any())
            {
                SeedChartOfAccounts(context);
            }
        }

        private void SeedChartOfAccounts(AccountingDbContext context)
        {
            var accounts = new[]
            {
                // الأصول
                new Models.Account { Code = "1", Name = "الأصول", AccountType = Models.AccountType.Asset, ParentId = null },
                new Models.Account { Code = "11", Name = "الأصول المتداولة", AccountType = Models.AccountType.Asset, ParentId = null },
                new Models.Account { Code = "111", Name = "النقدية", AccountType = Models.AccountType.Asset, ParentId = null },
                new Models.Account { Code = "112", Name = "البنوك", AccountType = Models.AccountType.Asset, ParentId = null },
                new Models.Account { Code = "113", Name = "العملاء", AccountType = Models.AccountType.Asset, ParentId = null },
                new Models.Account { Code = "114", Name = "المخزون", AccountType = Models.AccountType.Asset, ParentId = null },
                
                // الخصوم
                new Models.Account { Code = "2", Name = "الخصوم", AccountType = Models.AccountType.Liability, ParentId = null },
                new Models.Account { Code = "21", Name = "الخصوم المتداولة", AccountType = Models.AccountType.Liability, ParentId = null },
                new Models.Account { Code = "211", Name = "الموردون", AccountType = Models.AccountType.Liability, ParentId = null },
                new Models.Account { Code = "212", Name = "مصاريف مستحقة", AccountType = Models.AccountType.Liability, ParentId = null },
                
                // حقوق الملكية
                new Models.Account { Code = "3", Name = "حقوق الملكية", AccountType = Models.AccountType.Equity, ParentId = null },
                new Models.Account { Code = "31", Name = "رأس المال", AccountType = Models.AccountType.Equity, ParentId = null },
                new Models.Account { Code = "32", Name = "الأرباح المحتجزة", AccountType = Models.AccountType.Equity, ParentId = null },
                
                // الإيرادات
                new Models.Account { Code = "4", Name = "الإيرادات", AccountType = Models.AccountType.Revenue, ParentId = null },
                new Models.Account { Code = "41", Name = "إيرادات المبيعات", AccountType = Models.AccountType.Revenue, ParentId = null },
                
                // المصاريف
                new Models.Account { Code = "5", Name = "المصاريف", AccountType = Models.AccountType.Expense, ParentId = null },
                new Models.Account { Code = "51", Name = "تكلفة البضاعة المباعة", AccountType = Models.AccountType.Expense, ParentId = null },
                new Models.Account { Code = "52", Name = "مصاريف إدارية", AccountType = Models.AccountType.Expense, ParentId = null },
                new Models.Account { Code = "53", Name = "مصاريف بيع وتسويق", AccountType = Models.AccountType.Expense, ParentId = null }
            };

            context.Accounts.AddRange(accounts);
            context.SaveChanges();
        }

        private string HashPassword(string password)
        {
            // Simple hash for demo - use proper hashing in production
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }
    }
}
