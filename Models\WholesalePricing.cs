using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    [Table("WholesalePricing")]
    public class WholesalePricing
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string TierName { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,4)")]
        public decimal MinimumQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,4)")]
        public decimal MaximumQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal FixedDiscountAmount { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [StringLength(500)]
        public string? Notes { get; set; }

        // Foreign Keys
        public int ProductId { get; set; }
        public int? CustomerTypeId { get; set; }

        // Navigation properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        // Computed properties
        [NotMapped]
        public decimal EffectivePrice => UnitPrice - FixedDiscountAmount - (UnitPrice * DiscountPercentage / 100);

        [NotMapped]
        public string QuantityRange => MaximumQuantity > 0 
            ? $"{MinimumQuantity:N0} - {MaximumQuantity:N0}" 
            : $"{MinimumQuantity:N0}+";

        [NotMapped]
        public string DiscountDescription
        {
            get
            {
                if (DiscountPercentage > 0 && FixedDiscountAmount > 0)
                    return $"{DiscountPercentage:N1}% + {FixedDiscountAmount:N2} ر.س";
                else if (DiscountPercentage > 0)
                    return $"{DiscountPercentage:N1}%";
                else if (FixedDiscountAmount > 0)
                    return $"{FixedDiscountAmount:N2} ر.س";
                else
                    return "بدون خصم";
            }
        }
    }
}
