using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using AccountingSystem.Models;
using AccountingSystem.Views.Pages;

namespace AccountingSystem.Views
{
    public partial class MainWindow : Window
    {
        private User? _currentUser;
        private DispatcherTimer _timer;

        public MainWindow()
        {
            InitializeComponent();
            
            // Get current user
            _currentUser = Application.Current.Properties["CurrentUser"] as User;
            
            if (_currentUser != null)
            {
                UserNameTextBlock.Text = $"مرحباً، {_currentUser.FullName}";
                UserRoleTextBlock.Text = GetRoleDescription(_currentUser.Role);
            }

            // Setup timer for status bar
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();

            // Load dashboard by default
            LoadPage("Dashboard");
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            DateTimeTextBlock.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
        }

        private string GetRoleDescription(UserRole role)
        {
            return role switch
            {
                UserRole.Admin => "مدير النظام",
                UserRole.Accountant => "محاسب",
                UserRole.SalesEmployee => "موظف مبيعات",
                UserRole.InventoryManager => "مدير مخزون",
                UserRole.Viewer => "مستخدم عرض",
                _ => "مستخدم"
            };
        }

        private void MenuButton_Click(object sender, RoutedEventArgs e)
        {
            // Toggle navigation panel visibility
            // This can be implemented later for responsive design
        }

        private void NavigationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string pageTag)
            {
                LoadPage(pageTag);
            }
        }

        private void LoadPage(string pageTag)
        {
            try
            {
                Page? page = pageTag switch
                {
                    "Dashboard" => new DashboardPage(),
                    "ChartOfAccounts" => new ChartOfAccountsPage(),
                    "JournalEntries" => new JournalEntriesPage(),
                    "Customers" => new CustomersPage(),
                    "Suppliers" => new SuppliersPage(),
                    "Products" => new ProductsPage(),
                    "Categories" => new CategoriesPage(),
                    "WholesaleCustomers" => new WholesaleCustomersPage(),
                    "WholesaleInvoices" => new WholesaleInvoicesPage(),
                    "WholesalePricing" => new WholesalePricingPage(),
                    "WholesaleReports" => new WholesaleReportsPage(),
                    "SalesInvoices" => new SalesInvoicesPage(),
                    "PurchaseInvoices" => new PurchaseInvoicesPage(),
                    "Reports" => new ReportsPage(),
                    "Settings" => new SettingsPage(),
                    _ => new DashboardPage()
                };

                if (page != null)
                {
                    MainFrame.Navigate(page);
                    StatusTextBlock.Text = $"تم تحميل {GetPageTitle(pageTag)}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الصفحة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetPageTitle(string pageTag)
        {
            return pageTag switch
            {
                "Dashboard" => "لوحة التحكم",
                "ChartOfAccounts" => "شجرة الحسابات",
                "JournalEntries" => "القيود اليومية",
                "Customers" => "العملاء",
                "Suppliers" => "الموردين",
                "Products" => "الأصناف",
                "Categories" => "فئات الأصناف",
                "WholesaleCustomers" => "عملاء الجملة",
                "WholesaleInvoices" => "فواتير الجملة",
                "WholesalePricing" => "أسعار الجملة",
                "WholesaleReports" => "تقارير الجملة",
                "SalesInvoices" => "فواتير المبيعات",
                "PurchaseInvoices" => "فواتير المشتريات",
                "Reports" => "التقارير المالية",
                "Settings" => "الإعدادات",
                _ => "لوحة التحكم"
            };
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // Clear current user
                Application.Current.Properties.Remove("CurrentUser");

                // Show login window
                var loginWindow = new LoginWindow();
                loginWindow.Show();

                // Close main window
                Close();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }
}
