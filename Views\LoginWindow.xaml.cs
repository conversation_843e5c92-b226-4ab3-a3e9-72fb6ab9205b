using System.Windows;
using System.Windows.Input;
using AccountingSystem.Data;
using AccountingSystem.Models;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace AccountingSystem.Views
{
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();
            
            // Set default values for testing
            UsernameTextBox.Text = "admin";
            PasswordBox.Password = "admin123";
            
            // Handle Enter key press
            KeyDown += LoginWindow_KeyDown;
        }

        private void LoginWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(UsernameTextBox.Text) || 
                string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                ShowError("يرجى إدخال اسم المستخدم وكلمة المرور");
                return;
            }

            ShowLoading(true);
            HideError();

            try
            {
                using var context = new AccountingDbContext();
                
                var hashedPassword = HashPassword(PasswordBox.Password);
                var user = await context.Users
                    .FirstOrDefaultAsync(u => u.Username == UsernameTextBox.Text && 
                                            u.Password == hashedPassword && 
                                            u.IsActive);

                if (user != null)
                {
                    // Update last login date
                    user.LastLoginDate = DateTime.Now;
                    await context.SaveChangesAsync();

                    // Store current user in application
                    Application.Current.Properties["CurrentUser"] = user;

                    // Open main window
                    var mainWindow = new MainWindow();
                    mainWindow.Show();
                    
                    // Close login window
                    Close();
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في الاتصال بقاعدة البيانات: {ex.Message}");
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }

        private void ShowError(string message)
        {
            ErrorMessageTextBlock.Text = message;
            ErrorMessageTextBlock.Visibility = Visibility.Visible;
        }

        private void HideError()
        {
            ErrorMessageTextBlock.Visibility = Visibility.Collapsed;
        }

        private void ShowLoading(bool show)
        {
            LoadingProgressBar.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            LoginButton.IsEnabled = !show;
        }
    }
}
